// Copyright (c) 2024 Lenovo. All rights reserved.
// Confidential and Restricted.
#pragma once

#include <stdio.h>
#include <stdlib.h>

#include <cstdint>
#include <map>
#include <mutex>
#include <set>
#include <string>
#include <vector>

#include <openssl/conf.h>
#include <openssl/pem.h>
#include <openssl/rsa.h>
#include <openssl/x509.h>


namespace datatunnel {
class Cert {
 public:
  Cert();
  ~Cert();

  uint32_t GenerateLocalX509Cert();
  const std::string& GetLocalX509CertFingerprint();
  static std::string CalculateX509CustomFingerprint(X509* x509);
  static X509* stringToX509(const std::string& certString);

  const std::string& GetPkeyStr() const { return pkey_str; }
  const std::string& GetX509Str() const { return x509_str; }

 private:
  static uint32_t GenerateRsaKey30_(int32_t modulus, EVP_PKEY** pkey);
  static uint32_t GenerateX509Cert(EVP_PKEY* key, X509* x509);
  static uint32_t X509ToDer(X509* x509, uint8_t* buf, uint32_t* size);
  static std::string evpPkeyToString(EVP_PKEY* pkey);
  static std::string x509ToString(X509* x509);

  static void StrToLower(std::string& str);
  static std::string ConvertBytesToHexStr(const unsigned char* bytes,
                                          uint64_t bsize);

  std::string pkey_str;
  std::string x509_str;
  std::string x509_fp;
  static std::mutex _muFp;
  static std::map<std::string, std::string> _peerFingerPrintsMap;
};

}  // namespace datatunnel